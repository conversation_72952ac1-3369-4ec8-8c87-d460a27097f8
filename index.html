<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SAM 2 Image Segmentation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        .upload-section:hover {
            border-color: #007bff;
        }
        .upload-section.dragover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        #imageInput {
            margin: 10px 0;
        }
        #imageCanvas {
            border: 1px solid #ddd;
            cursor: crosshair;
            max-width: 100%;
            margin: 20px 0;
            display: none;
        }
        .coordinates {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
            display: none;
        }
        .result-image {
            max-width: 100%;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .loading {
            text-align: center;
            display: none;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .instructions {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 SAM 2 Image Segmentation</h1>

        <div class="instructions">
            <h3>How to use:</h3>
            <ol>
                <li>Upload an image of a car</li>
                <li>Click on the part of the car you want to segment</li>
                <li>Wait for the AI to process and show the segmentation result</li>
            </ol>
        </div>

        <div class="upload-section" id="uploadSection">
            <p>📁 Choose an image file or drag and drop here</p>
            <input type="file" id="imageInput" accept="image/*" />
        </div>

        <canvas id="imageCanvas"></canvas>

        <div class="coordinates" id="coordinates">
            <strong>Selected Point:</strong> X: <span id="xCoord">-</span>, Y: <span id="yCoord">-</span>
        </div>

        <div style="text-align: center;">
            <button class="btn" id="segmentBtn" onclick="performSegmentation()" disabled>
                🔍 Segment Image
            </button>
            <button class="btn" id="resetBtn" onclick="resetImage()" style="background-color: #6c757d;" disabled>
                🔄 Reset
            </button>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Processing image with SAM 2... This may take a few moments.</p>
        </div>

        <div class="error" id="errorMsg"></div>

        <div class="results" id="results">
            <h3>Results:</h3>
            <div>
                <h4>Segmented Image (with overlay):</h4>
                <img id="resultImage" class="result-image" alt="Segmentation result" />
            </div>
            <div>
                <h4>Mask Only:</h4>
                <img id="maskImage" class="result-image" alt="Segmentation mask" />
            </div>
            <div id="scoreInfo" class="success"></div>
        </div>
    </div>

    <script>
        let canvas = document.getElementById('imageCanvas');
        let ctx = canvas.getContext('2d');
        let imageFile = null;
        let clickX = -1, clickY = -1;
        let originalImage = null;

        // File input handling
        document.getElementById('imageInput').addEventListener('change', handleFileSelect);

        // Drag and drop handling
        let uploadSection = document.getElementById('uploadSection');
        uploadSection.addEventListener('dragover', handleDragOver);
        uploadSection.addEventListener('drop', handleDrop);
        uploadSection.addEventListener('dragleave', handleDragLeave);

        // Canvas click handling
        canvas.addEventListener('click', handleCanvasClick);

        function handleDragOver(e) {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                showError('Please select a valid image file.');
                return;
            }

            imageFile = file;
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    // Calculate canvas size to fit image while maintaining aspect ratio
                    const maxWidth = 800;
                    const maxHeight = 600;
                    let { width, height } = img;

                    if (width > maxWidth) {
                        height = (height * maxWidth) / width;
                        width = maxWidth;
                    }
                    if (height > maxHeight) {
                        width = (width * maxHeight) / height;
                        height = maxHeight;
                    }

                    canvas.width = width;
                    canvas.height = height;
                    canvas.style.display = 'block';

                    // Store original image and scale factors
                    originalImage = {
                        img: img,
                        scaleX: img.width / width,
                        scaleY: img.height / height
                    };

                    // Draw image on canvas
                    ctx.drawImage(img, 0, 0, width, height);

                    // Reset state
                    clickX = clickY = -1;
                    document.getElementById('coordinates').style.display = 'none';
                    document.getElementById('segmentBtn').disabled = true;
                    document.getElementById('resetBtn').disabled = false;
                    hideError();
                    hideResults();
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function handleCanvasClick(e) {
            if (!originalImage) return;

            const rect = canvas.getBoundingClientRect();
            clickX = Math.round((e.clientX - rect.left) * originalImage.scaleX);
            clickY = Math.round((e.clientY - rect.top) * originalImage.scaleY);

            // Redraw image and add click point
            ctx.drawImage(originalImage.img, 0, 0, canvas.width, canvas.height);

            // Draw click point
            const displayX = (e.clientX - rect.left);
            const displayY = (e.clientY - rect.top);

            ctx.fillStyle = 'red';
            ctx.beginPath();
            ctx.arc(displayX, displayY, 5, 0, 2 * Math.PI);
            ctx.fill();

            // Show coordinates
            document.getElementById('xCoord').textContent = clickX;
            document.getElementById('yCoord').textContent = clickY;
            document.getElementById('coordinates').style.display = 'block';
            document.getElementById('segmentBtn').disabled = false;
        }

        function performSegmentation() {
            if (!imageFile || clickX === -1 || clickY === -1) {
                showError('Please select an image and click on a point first.');
                return;
            }

            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('segmentBtn').disabled = true;
            hideError();
            hideResults();

            // Prepare form data
            const formData = new FormData();
            formData.append('image', imageFile);
            formData.append('x', clickX);
            formData.append('y', clickY);

            // Send request
            fetch('/segment', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('segmentBtn').disabled = false;

                if (data.success) {
                    // Show results
                    document.getElementById('resultImage').src = data.result_url;
                    document.getElementById('maskImage').src = data.mask_url;
                    document.getElementById('scoreInfo').textContent =
                        `Segmentation confidence score: ${(data.score * 100).toFixed(1)}%`;
                    document.getElementById('results').style.display = 'block';
                } else {
                    showError(data.error || 'Segmentation failed');
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('segmentBtn').disabled = false;
                showError('Network error: ' + error.message);
            });
        }

        function resetImage() {
            if (originalImage) {
                ctx.drawImage(originalImage.img, 0, 0, canvas.width, canvas.height);
                clickX = clickY = -1;
                document.getElementById('coordinates').style.display = 'none';
                document.getElementById('segmentBtn').disabled = true;
                hideResults();
            }
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMsg');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorMsg').style.display = 'none';
        }

        function hideResults() {
            document.getElementById('results').style.display = 'none';
        }
    </script>
</body>
</html>
